<?php

/**
 * @file
 * This is the allowed_formats module.
 *
 * It enables limiting which text formats are available for each fields.
 */

use Dr<PERSON>al\Core\Field\FieldDefinitionInterface;
use <PERSON><PERSON><PERSON>\Core\Field\WidgetInterface;
use <PERSON><PERSON><PERSON>\Core\Form\FormStateInterface;
use <PERSON><PERSON><PERSON>\field\FieldConfigInterface;

/**
 * Implements hook_field_widget_third_party_settings_form().
 */
function allowed_formats_field_widget_third_party_settings_form(WidgetInterface $plugin, FieldDefinitionInterface $field_definition, $form_mode, $form, FormStateInterface $form_state) {
  if (in_array($field_definition->getType(), _allowed_formats_field_types())) {
    $element = [];
    $element['hide_help'] = [
      '#type' => 'checkbox',
      '#title' => t('Hide the help link <em>About text formats</em>.'),
      '#default_value' => $plugin->getThirdPartySetting('allowed_formats', 'hide_help'),
    ];
    $element['hide_guidelines'] = [
      '#type' => 'checkbox',
      '#title' => t('Hide text format guidelines.'),
      '#default_value' => $plugin->getThirdPartySetting('allowed_formats', 'hide_guidelines'),
    ];

    return $element;
  }
}

/**
 * Implements hook_field_widget_single_element_form_alter().
 */
function allowed_formats_field_widget_single_element_form_alter(&$element, FormStateInterface $form_state, $context) {
  /** @var \Drupal\Core\Field\FieldDefinitionInterface $field_definition */
  $field_definition = $context['items']->getFieldDefinition();

  // We can't use the protected isDefaultValueWidget() method.
  $is_default_value_widget = (bool) $form_state->get('default_value_widget');

  if (in_array($field_definition->getType(), _allowed_formats_field_types()) && !$is_default_value_widget) {
    /** @var \Drupal\Core\Field\WidgetInterface $widget */
    $widget = $context['widget'];
    $element['#allowed_format_hide_settings'] = $widget->getThirdPartySettings('allowed_formats');

    if (count(array_filter($element['#allowed_format_hide_settings']))) {
      $element['#after_build'][] = '_allowed_formats_remove_textarea_help';
    }
  }
}

/**
 * The #after_build callback for text widgets.
 */
function _allowed_formats_remove_textarea_help($form_element, FormStateInterface $form_state) {
  if (isset($form_element['format'])) {
    if ($form_element['#allowed_format_hide_settings']['hide_help']) {
      unset($form_element['format']['help']);
    }
    if ($form_element['#allowed_format_hide_settings']['hide_guidelines']) {
      unset($form_element['format']['guidelines']);
    }

    // If nothing is left in the wrapper, hide it as well.
    if (isset($form_element['#allowed_formats'])
      && count($form_element['#allowed_formats']) == 1
      && $form_element['#allowed_format_hide_settings']['hide_help']
      && $form_element['#allowed_format_hide_settings']['hide_guidelines']) {
      unset($form_element['format']['#type']);
      unset($form_element['format']['#theme_wrappers']);
    }
  }

  return $form_element;
}

/**
 * Define what field types we want to modify.
 *
 * @return array
 *   Array of modifiable field types.
 */
function _allowed_formats_field_types() {
  return ['text', 'text_long', 'text_with_summary'];
}

/**
 * Implements hook_ENTITY_TYPE_presave().
 */
function allowed_formats_field_config_presave(FieldConfigInterface $field_config): void {
  // Ensure backwards compatibility for default configuration not converted yet
  // to Drupal 10.1.x.
  // @see https://www.drupal.org/node/3318572
  // @see https://www.drupal.org/project/allowed_formats/issues/3324446
  _allowed_formats_convert_formats2core($field_config);
}

/**
 * Converts allowed formats to Drupal 10.1.x.
 *
 * @param \Drupal\field\FieldConfigInterface $field_config
 *   The field configuration entity.
 *
 * @return bool
 *   Whether the field configuration has been changed.
 *
 * @see https://www.drupal.org/node/3318572
 */
function _allowed_formats_convert_formats2core(FieldConfigInterface $field_config): bool {
  // Can't use $field_config->getThirdPartySettings() because that doesn't
  // reveal if allowed_formats module key exists. If the module key exists, even
  // if it's empty, we want to remove it.
  if (!array_key_exists('allowed_formats', $field_config->get('third_party_settings') ?? [])) {
    return FALSE;
  }

  $allowed_formats = $field_config->getThirdPartySetting('allowed_formats', 'allowed_formats', []);
  // On some installations it might be possible that earlier module versions
  // stored the formats as they are submitted from a checkbox list:
  // @code
  // ['format1' => '0', 'format2' => 'format2', 'format3' => '0']
  // @encode
  $allowed_formats = array_values(array_filter($allowed_formats));
  // At the time when the site builder updates this module, the allowed
  // format field setting, provided by core, might already contain data, but
  // that is irrelevant because the allowed formats provided by this module
  // takes precedence in the form. So, it's safe to override potential field
  // settings added via new Drupal >10.0.0 feature.
  $field_config
    ->setSetting('allowed_formats', $allowed_formats)
    ->unsetThirdPartySetting('allowed_formats', 'allowed_formats');

  return TRUE;
}
