<?php

namespace Drupal\brevo\Form;

use Drupal\Core\Config\ConfigFactoryInterface;
use <PERSON><PERSON>al\Core\Config\TypedConfigManagerInterface;
use Drupal\Core\Extension\ModuleHandlerInterface;
use <PERSON><PERSON>al\Core\Form\ConfigFormBase;
use <PERSON>upal\Core\Form\FormStateInterface;
use Drupal\Core\Link;
use Drupal\Core\Url;
use Dr<PERSON>al\filter\FilterPluginManager;
use Drupal\brevo\BrevoHandlerInterface;
use Drupal\symfony_mailer\Entity\MailerTransport;
use Symfony\Component\DependencyInjection\ContainerInterface;

/**
 * Provides Brevo configuration form.
 */
class BrevoAdminSettingsForm extends ConfigFormBase {

  /**
   * Brevo handler.
   *
   * @var \Drupal\brevo\BrevoHandlerInterface
   */
  protected $brevoHandler;

  /**
   * The filter plugin manager.
   *
   * @var \Drupal\filter\FilterPluginManager
   */
  protected $filterManager;

  /**
   * The module handler.
   *
   * @var \Drupal\Core\Extension\ModuleHandlerInterface
   */
  protected $moduleHandler;

  /**
   * {@inheritdoc}
   */
  public static function create(ContainerInterface $container) {
    return new static(
      $container->get('config.factory'),
      $container->get('config.typed'),
      $container->get('brevo.mail_handler'),
      $container->get('plugin.manager.filter'),
      $container->get('module_handler'),
    );
  }

  /**
   * {@inheritdoc}
   */
  public function __construct(ConfigFactoryInterface $config_factory, TypedConfigManagerInterface $typedConfigManager, BrevoHandlerInterface $brevo_handler, FilterPluginManager $filter_manager, ModuleHandlerInterface $module_handler) {
    parent::__construct($config_factory, $typedConfigManager);
    $this->brevoHandler = $brevo_handler;
    $this->filterManager = $filter_manager;
    $this->moduleHandler = $module_handler;
  }

  /**
   * {@inheritdoc}
   */
  protected function getEditableConfigNames() {
    return [
      BrevoHandlerInterface::CONFIG_NAME,
    ];
  }

  /**
   * {@inheritdoc}
   */
  public function getFormId() {
    return 'brevo_admin_settings_form';
  }

  /**
   * {@inheritdoc}
   */
  public function validateForm(array &$form, FormStateInterface $form_state) {
    parent::validateForm($form, $form_state);

    $entered_api_key = $form_state->getValue('api_key');
    if (!empty($entered_api_key) && $this->brevoHandler->validateBrevoApiKey($entered_api_key) === FALSE) {
      $form_state->setErrorByName('api_key', $this->t("Couldn't connect to the Brevo API. Please check your API settings."));
    }
  }

  /**
   * {@inheritdoc}
   */
  public function buildForm(array $form, FormStateInterface $form_state) {
    $this->brevoHandler->validateBrevoLibrary(TRUE);
    $config = $this->config(BrevoHandlerInterface::CONFIG_NAME);

    $form['description'] = [
      '#markup' => $this->t('Please refer to @link for your settings.', [
        '@link' => Link::fromTextAndUrl($this->t('dashboard'), Url::fromUri('https://app-smtp.brevo.com/parameters', [
          'attributes' => [
            'onclick' => "target='_blank'",
          ],
        ]))->toString(),
      ]),
    ];

    $form['api_key'] = [
      '#title' => $this->t('Brevo API Key'),
      '#type' => 'textfield',
      '#required' => TRUE,
      '#description' => $this->t('Enter your @link.', [
        '@link' => Link::fromTextAndUrl($this->t('API key'), Url::fromUri('https://app.brevo.com/settings/keys/api'))->toString(),
      ]),
      '#default_value' => $config->get('api_key'),
    ];

    // Load not-editable configuration object to check actual api key value
    // including overrides.
    $not_editable_config = $this->configFactory()->get(BrevoHandlerInterface::CONFIG_NAME);

    // Don't show other settings until we don't set API key.
    if (empty($not_editable_config->get('api_key'))) {
      return parent::buildForm($form, $form_state);
    }

    // If "API Key" is overridden in settings.php it won't be visible in form.
    // We have to make the field optional and allow to configure other settings.
    if (empty($config->get('api_key')) && !empty($not_editable_config->get('api_key'))) {
      $form['api_key']['#required'] = FALSE;
    }

    $form['debug_mode'] = [
      '#title' => $this->t('Enable Debug Mode'),
      '#type' => 'checkbox',
      '#default_value' => $config->get('debug_mode'),
      '#description' => $this->t('Enable to log every email and queuing.'),
    ];

    $form['test_mode'] = [
      '#title' => $this->t('Enable Test Mode'),
      '#type' => 'checkbox',
      '#default_value' => $config->get('test_mode'),
      '#description' => $this->t('Brevo will accept the message but will not send it. This is useful for testing purposes.'),
    ];

    $form['advanced_settings'] = [
      '#type' => 'details',
      '#title' => $this->t('Advanced settings'),
      '#collapsible' => TRUE,
      '#collapsed' => TRUE,
    ];

    $form['advanced_settings']['format'] = [
      '#type' => 'fieldset',
      '#title' => $this->t('Format'),
    ];

    $options = [
      '' => $this->t('None'),
    ];
    $filter_formats = filter_formats();
    foreach ($filter_formats as $filter_format_id => $filter_format) {
      $options[$filter_format_id] = $filter_format->label();
    }

    // Add additional description text if there is a recommended filter plugin.
    // To be sure we are using the correct plugin name, let's use the plugin definition.
    $recommendation = !$this->filterManager->hasDefinition('filter_autop') ? ''
      : $this->t('Recommended format filters: @filter.', ['@filter' => $this->filterManager->getDefinition('filter_autop')['title'] ?? '']);

    $form['advanced_settings']['format']['format_filter'] = [
      '#title' => $this->t('Format filter'),
      '#type' => 'select',
      '#options' => $options,
      '#default_value' => $config->get('format_filter'),
      '#description' => $this->t('@text_format to use to render the message. @recommendation', [
        '@text_format' => Link::fromTextAndUrl($this->t('Text format'), Url::fromRoute('filter.admin_overview'))->toString(),
        '@recommendation' => $recommendation,
      ]),
    ];

    $form['advanced_settings']['format']['use_theme'] = [
      '#title' => $this->t('Use theme'),
      '#type' => 'checkbox',
      '#default_value' => $config->get('use_theme'),
      '#description' => $this->t('Enable to pass the message through a theme function. Default "brevo" or pass one with $message["params"]["theme"].'),
    ];

    if ($this->moduleHandler->moduleExists('mailsystem')) {
      $form['advanced_settings']['use_queue'] = [
        '#title' => $this->t('Enable Queue'),
        '#type' => 'checkbox',
        '#default_value' => $config->get('use_queue'),
        '#description' => $this->t('Enable to queue emails and send them out during cron run. You can also enable queue for specific email keys by selecting Brevo mailer (queued) plugin in @link.', [
          '@link' => Link::fromTextAndUrl($this->t('mail system configuration'), Url::fromRoute('mailsystem.settings'))->toString(),
        ]),
      ];
    }

    return parent::buildForm($form, $form_state);
  }

  /**
   * {@inheritdoc}
   */
  public function submitForm(array &$form, FormStateInterface $form_state) {
    $config_keys = ['api_key', 'debug_mode', 'test_mode', 'format_filter', 'use_queue', 'use_theme'];
    $brevo_config = $this->config(BrevoHandlerInterface::CONFIG_NAME);
    foreach ($config_keys as $config_key) {
      if ($form_state->hasValue($config_key)) {
        $brevo_config->set($config_key, $form_state->getValue($config_key));
      }
    }
    $brevo_config->save();

    // Synchronize changes with the symfony mailer transport, if necessary.
    if ($this->moduleHandler->moduleExists('symfony_mailer')) {
      $brevo_transport = MailerTransport::load('brevo');
      if (empty($brevo_transport)) {
        // Create a new Brevo transport if it does not exist yet.
        $brevo_transport = MailerTransport::create([
          'id' => 'brevo',
          'label' => 'Brevo',
          'plugin' => 'dsn',
        ]);
      }

      // Update the DSN of the transport with new API key.
      $brevo_transport->set('configuration', ['dsn' => 'brevo+api://'.$form_state->getValue('api_key').'@default']);
      $brevo_transport->save();
    }

    $this->messenger()->addMessage($this->t('The configuration options have been saved.'));
  }

}
