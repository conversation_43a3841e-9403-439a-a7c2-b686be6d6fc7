/**
 * @file
 * Seven theme styles.
 */

/* Align all tables cells top */
table td {
  vertical-align: top;
}

td > .form-item:first-of-type,
tr.odd .form-item:first-of-type,
tr.even .form-item:first-of-type {
  margin-top: 0;
}

td > .form-item:last-of-type,
tr.odd .form-item:last-of-type,
tr.even .form-item:last-of-type {
  margin-bottom: 0;
}

tr.odd .form--inline > .form-item,
tr.even .form--inline > .form-item {
  margin-top: 0;
  margin-bottom: 0;
}

/* Add margin around HR tags */
table hr {
  margin: 0.5em 0;
}

form hr {
  margin: 1em 0;
}

/* Add background to nested details */
details.seven-details details.seven-details {
  background-color: #f8f8f8;
}

details details details {
  background-color: #fff;
}

code {
  padding: 2px 4px;
  color: #333;
  border: 1px solid #333;
  background-color: #f8f8f8;
  font-weight: bold;
}

/* Accordion icon */
.ui-accordion-icons .ui-icon {
  display: inline-block;
}

/* CodeMirror viewer */
pre.webform-codemirror-runmode {
  padding: 0.5em 1.5em;
}

/* Hide tabs and elements from printing. */
@media print {
  body {
    padding-top: 0 !important; /* Using !import to ensure that <body style="padding-top: *"> set via the toolbar.module is overridden. */
  }

  .region.region-highlighted,
  .shortcut-action,
  .dropbutton-wrapper,
  .block-system-breadcrumb-block,
  .block-local-tasks-block,
  .pager,
  .button-action,
  .webform-submission-navigation,
  .webform-filter-form {
    display: none !important; /* Using !important to ensure with these elements are hidden with getting into a specificity war */
  }
}

/**
 * Form action are moved to a dialogs footer and don't need to be visually
 * hidden.
 *
 * @see Drupal.behaviors.dialog.prepareDialogButtons
 */
.webform-ui-dialog .form-actions input[type=submit],
.webform-ui-dialog .form-actions a.button {
  display: none !important;
}

/**
 * Primary button font color is not white.
 *
 * @see https://www.drupal.org/project/webform/issues/3228968
 */
.webform-ui-dialog a.button--primary {
  color: #fff;
}

/* System tray divider */

.ui-dialog.ui-dialog-off-canvas.webform-off-canvas {
  /* Place un admin menu toolbar (z-index: 502) */
  z-index: 501 !important;
}

.ui-dialog.ui-dialog-off-canvas .ui-resizable-w {
  border: 1px solid #6b6b6b;
  border-width: 1px 2px;
  background-color: #bfbfba;
}

/* System tray title bar */
.ui-dialog.ui-dialog-off-canvas {
  background: #fff;
}

.ui-dialog.ui-dialog-off-canvas .ui-dialog-titlebar {
  border-radius: 0;
}

/* System tray actions */
.ui-dialog.ui-dialog-off-canvas .ui-dialog-content .form-actions {
  margin: 1em 0;
}

/* jQuery UI autocomplete states */
.ui-autocomplete a /* For filter autocomplete. */,
.ui-autocomplete .ui-menu-item-wrapper /* For #states value autocomplete. */ {
  display: block;
  color: #0074bd;
  border: 1px solid transparent;
}

.ui-autocomplete a.ui-state-hover,
.ui-autocomplete a.ui-state-active,
.ui-autocomplete a.ui-state-focus,
.ui-autocomplete .ui-menu-item-wrapper.ui-state-hover,
.ui-autocomplete .ui-menu-item-wrapper.ui-state-active,
.ui-autocomplete .ui-menu-item-wrapper.ui-state-focus {
  margin: 0;
  text-decoration: underline;
  color: #0074bd;
  border: solid 1px #ed5;
  background: #fe6;
}

/* jQuery UI button states */
.webform-buttons .ui-button.ui-state-default {
  border: solid 1px #bfbfba;
  background: #f5f5f2;
}

.webform-buttons .ui-button.ui-state-active {
  color: #840;
  border: solid 1px #ed5;
  background: #fe6;
}

/* Tweak details toggle state. */
.webform-details-toggle-state {
  color: #0074bd;
}

.webform-details-toggle-state:hover,
.webform-details-toggle-statelink:focus {
  text-decoration: underline;
}

/* Token tree */
.token-tree ul {
  margin: 0;
}
