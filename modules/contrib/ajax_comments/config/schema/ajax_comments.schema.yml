# Schema for the configuration files of the AJAX Comments module.

ajax_comments.settings:
  type: config_object
  label: 'AJAX comments settings'
  mapping:
    notify:
      type: boolean
      label: 'Add notification message when comment posted'
    enable_scroll:
      type: boolean
      label: 'Enable scrolling events'
    reply_autoclose:
      type: boolean
      label: 'Autoclose any opened reply forms'

field.formatter.third_party.ajax_comments:
  type: mapping
  mapping:
    enable_ajax_comments:
      label: Enable Ajax Comments
      type: string
