<?php

/**
 * @file
 * Contains \Drupal\view_password\view_password.module.
 */

use <PERSON>upal\Core\Routing\RouteMatchInterface;
use <PERSON>upal\Component\Utility\Html;

/**
 * Implements hook_help().
 */
function view_password_help($route_name, RouteMatchInterface $route_match) {
  switch ($route_name) {

    case 'help.page.view_password':
      $output = '';
      $output .= '<h3>' . t('About') . '</h3>';
      $output .= '<p>' . t('View Password module will help the user to see what password they entered.') . '</p>';
      return $output;

    default:
  }
}

/**
 * Implements hook_form_alter().
 */
function view_password_form_alter(&$form, &$form_state, $form_id) {

  $form_ids = \Drupal::config('view_password.settings')->get('form_ids');
  $frm_id = explode(',', $form_ids ?? '');

  foreach ($frm_id as $frmid) {
    if ($form_id == $frmid) {
      $form['#cache']['tags'][] = 'config:view_password.settings';
      $span_classes = \Drupal::config('view_password.settings')->get('span_classes');

      // Unconditionally attach an asset to the page.
      $form['#attached']['drupalSettings']['view_password']['span_classes'] = Html::escape($span_classes);

      // Adding a class else the js is adding to all forms.
      $form['#attributes']['class'][] = 'pwd-see';

      // Adding js to the form that has the id in the config form.
      $form['#attached']['library'][] = 'view_password/pwd_lb';
      $form['#attached']['drupalSettings']['view_password']['showPasswordLabel'] = t("Show password");
      $form['#attached']['drupalSettings']['view_password']['hidePasswordLabel'] = t("Hide password");

      // overriden icons
      $exposed_icon = \Drupal::config('view_password.settings')->get('icon_exposed') ?? '';
      $form['#attached']['drupalSettings']['view_password']['icon_exposed'] = $exposed_icon;
      $hidden_icon = \Drupal::config('view_password.settings')->get('icon_hidden') ?? '';
      $form['#attached']['drupalSettings']['view_password']['icon_hidden'] = $hidden_icon;


    }
  }

}
