<?php

use Dr<PERSON>al\Core\Form\FormStateInterface;

/**
 * Implements hook_form_alter() to modify the user registration form.
 */
function account_popup_form_alter(&$form, FormStateInterface $form_state, $form_id) {
  // Check if it's the user registration form.
  if ($form_id === 'user_register_form') {
    // Add custom submit handler to trigger the popup.
    $form['#attached']['library'][] = 'account_popup/popup_library';
  }
}
