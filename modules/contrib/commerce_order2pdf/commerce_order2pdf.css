/**
 * Tables.
 */
table {
  width: 100%;
  font-size: 0.923em;
  margin: 0 0 10px;
  border: 1px solid #bebfb9;
}
table td,
table th {
  vertical-align: middle;
  padding: 8px 10px;
  border: 0;
  color: #000;
}
tr.even,
tr.odd {
  border-width: 0 1px 0 1px;
  border-style: solid;
  border-color: #bebfb9;
  background: #f3f4ee;
}
tr.odd {
  background: #fff;
}
table th {
  text-transform: uppercase;
  background: #e1e2dc;
  font-weight: normal;
  border-width: 1px;
  border-style: solid;
  border-color: #bebfb9;
  padding: 3px 10px;
}
table th a {
  display: block;
  position: relative;
}
table th.active a {
  padding: 0 25px 0 0; /* LTR */
}
table th.active img {
  position: absolute;
  top: 3px;
  right: 3px; /* LTR */
}
/**
 * Exception for webkit bug with the right border of the last cell
 * in some tables, since it's webkit only, we can use :last-child
 */
tr td:last-child {
  border-right: 1px solid #bebfb9; /* LTR */
}
.field--name-total-price .order-total-line__subtotal,
.field--name-total-price .order-total-line__total {
  width: 33%;
  margin-left: auto;
}
.order-total-line__total .order-total-line-label,
.field--name-billing-profile .field__label,
.field--name-total-price .field__label {
  font-weight: bold;
}
.contextual-links-wrapper {
  display: none;
}
