/* Simple card grid styling for Views */
.view-products-cards, .view-products-cards .views-view-grid, .view-products-cards .views-row {
  box-sizing: border-box;
}
.view-products-cards .views-row {
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 10px rgba(0,0,0,.06);
  padding: 8px;
}
.view-products-cards img {
  display: block;
  width: 100%;
  height: auto;
  border-radius: 8px;
}
.view-products-cards .product-title {
  font-weight: 600;
  margin-top: 6px;
}
.view-products-cards .product-price {
  font-size: 0.95rem;
  opacity: .85;
}
