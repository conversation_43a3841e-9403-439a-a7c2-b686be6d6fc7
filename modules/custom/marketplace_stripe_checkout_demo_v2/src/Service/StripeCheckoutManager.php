<?php

namespace Drupal\marketplace_stripe_checkout_demo_v2\Service;

use Drupal\Core\Config\ConfigFactoryInterface;
use Drupal\Core\Entity\EntityTypeManagerInterface;
use Drupal\Core\Logger\LoggerChannelFactoryInterface;
use Drupal\commerce_product\Entity\ProductInterface;
use Drupal\marketplace_single_store_plus\Service\MarketplacePlusManager;

class StripeCheckoutManager {

  public function __construct(
    protected ConfigFactoryInterface $configFactory,
    protected EntityTypeManagerInterface $etm,
    protected LoggerChannelFactoryInterface $loggerFactory,
    protected MarketplacePlusManager $marketplace
  ) {}

  protected function logger() { return $this->loggerFactory->get('stripe_checkout_demo_v2'); }

  protected function amountFromProduct(ProductInterface $product): ?int {
    if ($product->hasField('variations') && !$product->get('variations')->isEmpty()) {
      $variation = $product->get('variations')->entity;
      if ($variation && $variation->hasField('price')) {
        $price = $variation->get('price')->first();
        if ($price) { return (int) round(((float) $price->number) * 100); }
      }
    }
    return NULL;
  }

  protected function currencyFromProduct(ProductInterface $product): string {
    if ($product->hasField('variations') && !$product->get('variations')->isEmpty()) {
      $variation = $product->get('variations')->entity;
      if ($variation && $variation->hasField('price')) {
        $price = $variation->get('price')->first();
        if ($price) { return strtolower($price->currency_code); }
      }
    }
    return 'eur';
  }

  protected function resolveCommissionPercent(ProductInterface $product): float {
    // If commission rules service exists, use it.
    $container = \Drupal::getContainer();
    if ($container->has('marketplace_commission_rules.resolver')) {
      try {
        return (float) $container->get('marketplace_commission_rules.resolver')->getCommissionForProduct($product);
      } catch (\Throwable $e) {
        $this->logger()->warning('Commission resolver error: ' . $e->getMessage());
      }
    }
    // Fallback to global setting from PLUS.
    return (float) $this->marketplace->getCommissionPercent();
  }

  public function createCheckoutSession(ProductInterface $product): string {
    $cfg = $this->configFactory->get('marketplace_stripe_checkout_demo_v2.settings');
    $secret = $cfg->get('secret_key');
    if (empty($secret)) {
      throw new \RuntimeException('Stripe Secret Key missing. Configure it first.');
    }
    $success_route = $cfg->get('success_route') ?: 'marketplace_stripe_checkout_demo_v2.success';
    $cancel = $cfg->get('cancel_url') ?: '/';

    $amount = $this->amountFromProduct($product);
    if (!$amount) { throw new \RuntimeException('Product has no price (first variation).'); }
    $currency = $this->currencyFromProduct($product);

    // Vendor account (if any).
    $acct = NULL;
    if ($product->hasField('field_vendor_user') && !$product->get('field_vendor_user')->isEmpty()) {
      $vendor_uid = (int) $product->get('field_vendor_user')->target_id;
      $user = $this->etm->getStorage('user')->load($vendor_uid);
      if ($user && $user->hasField('field_stripe_account_id')) {
        $acct = $user->get('field_stripe_account_id')->value ?: NULL;
      }
    }

    $commission = $this->resolveCommissionPercent($product);
    $fee = (int) round($amount * ($commission / 100.0));

    \Stripe\Stripe::setApiKey($secret);
    $success_url = \Drupal::url($success_route, [], ['absolute' => TRUE]) . '?session_id={CHECKOUT_SESSION_ID}';
    $params = [
      'mode' => 'payment',
      'success_url' => $success_url,
      'cancel_url' => $cancel,
      'line_items' => [[
        'price_data' => [
          'currency' => $currency,
          'product_data' => ['name' => $product->label()],
          'unit_amount' => $amount,
        ],
        'quantity' => 1,
      ]],
    ];
    if ($acct) {
      $params['payment_intent_data'] = [
        'application_fee_amount' => $fee,
        'transfer_data' => ['destination' => $acct],
      ];
    }
    $session = \Stripe\Checkout\Session::create($params);
    return $session->url;
  }

  public function fetchSession(string $session_id): array {
    $cfg = $this->configFactory->get('marketplace_stripe_checkout_demo_v2.settings');
    $secret = $cfg->get('secret_key');
    if (empty($secret)) { throw new \RuntimeException('Stripe Secret Key missing.'); }
    \Stripe\Stripe::setApiKey($secret);
    $session = \Stripe\Checkout\Session::retrieve($session_id);
    $pi = NULL;
    if (!empty($session->payment_intent)) {
      $pi = \Stripe\PaymentIntent::retrieve($session->payment_intent);
    }
    return [
      'id' => $session->id,
      'status' => $session->status,
      'payment_status' => $session->payment_status,
      'amount_total' => $session->amount_total,
      'currency' => $session->currency,
      'payment_intent' => $pi ? [
        'id' => $pi->id,
        'status' => $pi->status,
        'amount' => $pi->amount,
        'charges' => $pi->charges->data[0]->id ?? NULL,
      ] : NULL,
    ];
  }

}
