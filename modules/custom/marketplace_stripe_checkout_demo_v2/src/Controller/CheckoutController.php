<?php

namespace Drupal\marketplace_stripe_checkout_demo_v2\Controller;

use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Request;
use Drupal\Core\Controller\ControllerBase;
use Drupal\commerce_product\Entity\ProductInterface;

class CheckoutController extends ControllerBase {

  public function create(ProductInterface $commerce_product) {
    $mgr = \Drupal::service('marketplace_stripe_checkout_demo_v2.manager');
    try {
      $url = $mgr->createCheckoutSession($commerce_product);
      return new RedirectResponse($url);
    }
    catch (\Throwable $e) {
      $this->messenger()->addError($e->getMessage());
      return $this->redirect('<front>');
    }
  }

  public function success(Request $request) {
    $session_id = $request->query->get('session_id');
    if (!$session_id) {
      $this->messenger()->addWarning($this->t('Session ID missing.'));
      return $this->redirect('<front>');
    }
    $mgr = \Drupal::service('marketplace_stripe_checkout_demo_v2.manager');
    try {
      $data = $mgr->fetchSession($session_id);
    }
    catch (\Throwable $e) {
      $this->messenger()->addError($e->getMessage());
      return $this->redirect('<front>');
    }

    $build['title'] = ['#markup' => '<h2>' . $this->t('Paiement réussi') . '</h2>'];
    $build['summary'] = [
      '#type' => 'item',
      '#markup' => $this->t('Session: @sid<br>Status: @st (@pst)<br>Montant: @amt @cur', [
        '@sid' => $data['id'],
        '@st'  => $data['status'],
        '@pst' => $data['payment_status'],
        '@amt' => number_format(($data['amount_total'] ?? 0) / 100, 2, ',', ' '),
        '@cur' => strtoupper($data['currency'] ?? 'EUR'),
      ]),
    ];
    if (!empty($data['payment_intent'])) {
      $pi = $data['payment_intent'];
      $build['pi'] = [
        '#type' => 'details',
        '#open' => TRUE,
        '#title' => $this->t('PaymentIntent'),
        'pre' => [
          '#type' => 'html_tag',
          '#tag' => 'pre',
          '#value' => json_encode($pi, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES),
        ],
      ];
    }
    return $build;
  }

}
