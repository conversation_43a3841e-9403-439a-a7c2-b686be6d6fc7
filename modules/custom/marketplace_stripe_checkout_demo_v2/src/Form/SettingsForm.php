<?php

namespace Drupal\marketplace_stripe_checkout_demo_v2\Form;

use Drupal\Core\Form\ConfigFormBase;
use Drupal\Core\Form\FormStateInterface;

class SettingsForm extends ConfigFormBase {

  protected function getEditableConfigNames() { return ['marketplace_stripe_checkout_demo_v2.settings']; }
  public function getFormId() { return 'marketplace_stripe_checkout_demo_v2_settings'; }

  public function buildForm(array $form, FormStateInterface $form_state) {
    $c = $this->config('marketplace_stripe_checkout_demo_v2.settings');
    $form['secret_key'] = [
      '#type' => 'textfield',
      '#title' => $this->t('Stripe Secret Key (test)'),
      '#default_value' => $c->get('secret_key') ?? '',
      '#required' => TRUE,
    ];
    $form['publishable_key'] = [
      '#type' => 'textfield',
      '#title' => $this->t('Stripe Publishable Key (test)'),
      '#default_value' => $c->get('publishable_key') ?? '',
      '#required' => FALSE,
    ];
    $form['success_route'] = [
      '#type' => 'textfield',
      '#title' => $this->t('Success route'),
      '#default_value' => $c->get('success_route') ?? 'marketplace_stripe_checkout_demo_v2.success',
      '#description' => $this->t('Internal route to redirect after payment success.'),
      '#required' => TRUE,
    ];
    $form['cancel_url'] = [
      '#type' => 'textfield',
      '#title' => $this->t('Cancel URL'),
      '#default_value' => $c->get('cancel_url') ?? '/',
      '#required' => TRUE,
    ];
    return parent::buildForm($form, $form_state);
  }

  public function submitForm(array &$form, FormStateInterface $form_state) {
    $this->configFactory->getEditable('marketplace_stripe_checkout_demo_v2.settings')
      ->set('secret_key', $form_state->getValue('secret_key'))
      ->set('publishable_key', $form_state->getValue('publishable_key'))
      ->set('success_route', $form_state->getValue('success_route'))
      ->set('cancel_url', $form_state->getValue('cancel_url'))
      ->save();
    parent::submitForm($form, $form_state);
  }
}
