marketplace_stripe_checkout_demo_v2.create_session:
  path: '/marketplace/checkout-demo-v2/{commerce_product}'
  defaults:
    _controller: '\Drupal\marketplace_stripe_checkout_demo_v2\Controller\CheckoutController::create'
    _title: 'Checkout (demo v2)'
  requirements:
    _permission: 'access content'
  options:
    parameters:
      commerce_product:
        type: entity:commerce_product

marketplace_stripe_checkout_demo_v2.success:
  path: '/marketplace/checkout-success'
  defaults:
    _controller: '\Drupal\marketplace_stripe_checkout_demo_v2\Controller\CheckoutController::success'
    _title: 'Paiement réussi'
  requirements:
    _permission: 'access content'

marketplace_stripe_checkout_demo_v2.settings:
  path: '/admin/commerce/config/marketplace-stripe-demo-v2'
  defaults:
    _form: '\Drupal\marketplace_stripe_checkout_demo_v2\Form\SettingsForm'
    _title: 'Stripe Checkout Demo v2 settings'
  requirements:
    _permission: 'administer commerce'
