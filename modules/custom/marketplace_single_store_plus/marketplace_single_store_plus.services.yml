services:
  marketplace_plus.manager:
    class: Drupal\marketplace_single_store_plus\Service\MarketplacePlusManager
    arguments: ['@entity_type.manager', '@commerce_store.current_store', '@logger.factory', '@config.factory']

  marketplace_plus.block.products_by_vendor:
    class: Drupal\marketplace_single_store_plus\Plugin\Block\ProductsByVendorBlock
    arguments: ['@entity_type.manager', '@renderer']
    tags:
      - { name: block }
