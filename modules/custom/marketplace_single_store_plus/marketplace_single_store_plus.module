<?php

/**
 * @file
 * Contains marketplace_single_store_plus.module.
 */

use Drupal\Core\Entity\EntityInterface;
use Drupal\commerce_product\Entity\ProductInterface;

/**
 * Implements hook_entity_presave().
 */
function marketplace_single_store_plus_entity_presave(EntityInterface $entity)
{
  if ($entity instanceof ProductInterface) {
    try {
      $manager = \Drupal::service('marketplace_plus.manager');
      $manager->normalizeProduct($entity);
    } catch (\Throwable $e) {
      \Drupal::logger('marketplace_plus')->error('Product presave: ' . $e->getMessage());
    }
  }
}
