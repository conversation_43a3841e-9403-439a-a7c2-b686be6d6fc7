<?php

namespace Drupal\marketplace_single_store_plus\EventSubscriber;

use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Drupal\core_event_dispatcher\EntityHookEvents;
use Drupal\core_event_dispatcher\Event\Entity\EntityPresaveEvent;
use Drupal\marketplace_single_store_plus\Service\MarketplacePlusManager;
use Drupal\commerce_product\Entity\ProductInterface;

class ProductPresaveSubscriber implements EventSubscriberInterface {

  public function __construct(
    protected MarketplacePlusManager $manager,
    protected $loggerFactory
  ) {}

  public static function getSubscribedEvents() {
    $events[EntityHookEvents::ENTITY_PRE_SAVE][] = ['onPresave'];
    return $events;
  }

  public function onPresave(EntityPresaveEvent $event): void {
    $entity = $event->getEntity();
    if ($entity instanceof ProductInterface) {
      try {
        $this->manager->normalizeProduct($entity);
      }
      catch (\Throwable $e) {
        $this->loggerFactory->get('marketplace_plus')->error('Product presave: ' . $e->getMessage());
      }
    }
  }

}
