<?php

namespace Drupal\marketplace_single_store_plus\Form;

use Drupal\Core\Form\FormBase;
use <PERSON>upal\Core\Form\FormStateInterface;
use <PERSON>upal\Core\Session\AccountProxyInterface;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Drupal\user\Entity\User;

class UserStripeAccountForm extends FormBase
{

  public function __construct(protected AccountProxyInterface $currentUser) {}

  public static function create(ContainerInterface $container)
  {
    return new static($container->get('current_user'));
  }

  public function getFormId()
  {
    return 'marketplace_plus_user_stripe_form';
  }

  public function buildForm(array $form, FormStateInterface $form_state)
  {
    $uid = $this->currentUser->id();
    $user = User::load($uid);
    $value = $user && $user->hasField('field_stripe_account_id') ? $user->get('field_stripe_account_id')->value : '';

    $form['stripe'] = [
      '#type' => 'textfield',
      '#title' => $this->t('Stripe Account ID'),
      '#default_value' => $value,
      '#description' => $this->t('Exemple: acct_123...'),
      '#maxlength' => 255,
      '#required' => FALSE,
    ];
    $form['actions']['submit'] = [
      '#type' => 'submit',
      '#value' => $this->t('Enregistrer'),
    ];
    return $form;
  }

  public function submitForm(array &$form, FormStateInterface $form_state)
  {
    $uid = $this->currentUser->id();
    $user = User::load($uid);
    if ($user && $user->hasField('field_stripe_account_id')) {
      $user->set('field_stripe_account_id', $form_state->getValue('stripe'));
      $user->save();
      $this->messenger()->addStatus($this->t('Stripe Account ID mis à jour.'));
    }
  }
}
