uuid: 50bd0066-380b-41b2-87af-f11da8acd050
langcode: en
status: true
dependencies:
  module:
    - comment
    - node
    - user
_core:
  default_config_hash: S_NN2ubd_NovTfgzbHVlZMmIJJQS-3h9h3inzbaFUMY
id: comments_recent
label: 'Recent comments'
module: views
description: 'Recent comments.'
tag: default
base_table: comment_field_data
base_field: cid
display:
  default:
    id: default
    display_title: Default
    display_plugin: default
    position: 0
    display_options:
      title: 'Recent comments'
      fields:
        subject:
          id: subject
          table: comment_field_data
          field: subject
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: comment
          entity_field: subject
          plugin_id: field
          label: ''
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: false
            ellipsis: false
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          type: string
          settings:
            link_to_entity: true
        changed:
          id: changed
          table: comment_field_data
          field: changed
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: comment
          entity_field: changed
          plugin_id: field
          label: ''
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          type: timestamp_ago
          settings:
            future_format: '@interval hence'
            past_format: '@interval ago'
            granularity: 2
      pager:
        type: some
        options:
          offset: 0
          items_per_page: 10
      exposed_form:
        type: basic
      access:
        type: perm
        options:
          perm: 'access comments'
      cache:
        type: tag
      empty:
        area_text_custom:
          id: area_text_custom
          table: views
          field: area_text_custom
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: text_custom
          label: ''
          empty: true
          content: 'No comments available.'
          tokenize: false
      sorts:
        created:
          id: created
          table: comment_field_data
          field: created
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: comment
          entity_field: created
          plugin_id: date
          order: DESC
          expose:
            label: ''
            field_identifier: created
          exposed: false
        cid:
          id: cid
          table: comment_field_data
          field: cid
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: comment
          entity_field: cid
          plugin_id: field
          order: DESC
          expose:
            label: ''
            field_identifier: cid
          exposed: false
      filters:
        status:
          id: status
          table: comment_field_data
          field: status
          entity_type: comment
          entity_field: status
          plugin_id: boolean
          value: '1'
          group: 1
          expose:
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
        status_node:
          id: status_node
          table: node_field_data
          field: status
          relationship: node
          entity_type: node
          entity_field: status
          plugin_id: boolean
          value: '1'
          group: 1
          expose:
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
      style:
        type: html_list
        options:
          grouping: {  }
          row_class: ''
          default_row_class: true
          type: ul
          wrapper_class: item-list
          class: ''
      row:
        type: fields
        options:
          default_field_elements: true
          hide_empty: false
      query:
        type: views_query
      relationships:
        node:
          id: node
          table: comment_field_data
          field: node
          plugin_id: standard
          required: true
      display_extenders: {  }
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - user.permissions
      tags: {  }
  block_1:
    id: block_1
    display_title: Block
    display_plugin: block
    position: 1
    display_options:
      display_extenders: {  }
      block_description: 'Recent comments'
      block_category: 'Lists (Views)'
      allow:
        items_per_page: true
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - user.permissions
      tags: {  }
