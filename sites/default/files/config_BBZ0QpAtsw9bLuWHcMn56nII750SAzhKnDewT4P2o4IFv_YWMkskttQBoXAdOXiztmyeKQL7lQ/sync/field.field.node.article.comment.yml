uuid: 38eb1217-1bc2-4589-8837-9bbad7777de4
langcode: en
status: true
dependencies:
  config:
    - field.storage.node.comment
    - node.type.article
  module:
    - comment
_core:
  default_config_hash: UqXlkKC4v2-bDfWx4zcXQrD5YIi3d5byENEmWv-G_Uc
id: node.article.comment
field_name: comment
entity_type: node
bundle: article
label: Comments
description: ''
required: false
translatable: true
default_value:
  -
    status: 2
    cid: 0
    last_comment_timestamp: 0
    last_comment_name: null
    last_comment_uid: 0
    comment_count: 0
default_value_callback: ''
settings:
  default_mode: 1
  per_page: 50
  anonymous: 0
  form_location: true
  preview: 1
field_type: comment
