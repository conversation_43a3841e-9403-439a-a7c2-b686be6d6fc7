langcode: fr
status: true
dependencies:
  config:
    - node.type.prestataire
  module:
    - node
id: node.prestataire.field_logo
field_name: field_logo
entity_type: node
bundle: prestataire
label: 'Logo'
description: ''
required: false
translatable: true
default_value: {}
default_value_callback: ''

      settings:
        alt_field_required: false
        title_field_required: false

field_type: image
