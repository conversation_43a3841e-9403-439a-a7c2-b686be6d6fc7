uuid: 582cb771-f3d6-4724-a6c9-37d992ea5e94
langcode: en
status: true
dependencies:
  module:
    - user
_core:
  default_config_hash: suDsVMgawXoQt4rfmdfpr05EVX3z3KyfDDTYgeSM898
id: who_s_online
label: "Who's online block"
module: user
description: 'Shows the user names of the most recently active users, and the total number of active users.'
tag: default
base_table: users_field_data
base_field: uid
display:
  default:
    id: default
    display_title: Default
    display_plugin: default
    position: 0
    display_options:
      title: "Who's online"
      fields:
        name:
          id: name
          table: users_field_data
          field: name
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: user
          entity_field: name
          plugin_id: field
          label: ''
          exclude: false
          alter:
            alter_text: false
            make_link: false
            absolute: false
            word_boundary: false
            ellipsis: false
            strip_tags: false
            trim: false
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          type: user_name
      pager:
        type: some
        options:
          offset: 0
          items_per_page: 10
      exposed_form:
        type: basic
        options:
          submit_button: Apply
          reset_button: false
          reset_button_label: Reset
          exposed_sorts_label: 'Sort by'
          expose_sort_order: true
          sort_asc_label: Asc
          sort_desc_label: Desc
      access:
        type: perm
        options:
          perm: 'access user profiles'
      cache:
        type: tag
        options: {  }
      empty:
        area_text_custom:
          id: area_text_custom
          table: views
          field: area_text_custom
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: text_custom
          empty: true
          content: 'There are currently 0 users online.'
          tokenize: false
      sorts:
        access:
          id: access
          table: users_field_data
          field: access
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: user
          entity_field: access
          plugin_id: date
          order: DESC
          expose:
            label: ''
            field_identifier: access
          exposed: false
          granularity: second
      arguments: {  }
      filters:
        status:
          id: status
          table: users_field_data
          field: status
          entity_type: user
          entity_field: status
          plugin_id: boolean
          value: '1'
          group: 1
          expose:
            operator: '0'
            operator_limit_selection: false
            operator_list: {  }
        access:
          id: access
          table: users_field_data
          field: access
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: user
          entity_field: access
          plugin_id: date
          operator: '>='
          value:
            min: ''
            max: ''
            value: '-15 minutes'
            type: offset
          group: 1
          exposed: false
          expose:
            operator_id: access_op
            label: 'Last access'
            description: 'A user is considered online for this long after they have last viewed a page.'
            use_operator: false
            operator: access_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: access
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              administrator: '0'
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
      style:
        type: html_list
        options:
          grouping: {  }
          row_class: ''
          default_row_class: true
          type: ul
          wrapper_class: item-list
          class: ''
      row:
        type: fields
      query:
        type: views_query
        options:
          query_comment: ''
          disable_sql_rewrite: false
          distinct: false
          replica: false
          query_tags: {  }
      relationships: {  }
      header:
        result:
          id: result
          table: views
          field: result
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: result
          empty: false
          content: 'There are currently @total users online.'
      footer: {  }
      display_extenders: {  }
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - user.permissions
      tags: {  }
  who_s_online_block:
    id: who_s_online_block
    display_title: "Who's online"
    display_plugin: block
    position: 1
    display_options:
      display_description: 'A list of users that are currently logged in.'
      display_extenders: {  }
      block_description: "Who's online"
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - user.permissions
      tags: {  }
