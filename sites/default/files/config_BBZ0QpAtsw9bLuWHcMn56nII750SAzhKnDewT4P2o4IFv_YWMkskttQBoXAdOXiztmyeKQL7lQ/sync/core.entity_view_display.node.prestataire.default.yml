langcode: fr
status: true
dependencies:
  config:
    - node.type.prestataire
  module:
    - layout_builder
third_party_settings:
  layout_builder:
    enabled: true
    allow_custom: true
id: node.prestataire.default
targetEntityType: node
bundle: prestataire
mode: default
content:
  field_logo:
    type: image
    label: hidden
    weight: -10
    settings:
      image_style: medium
      image_link: content
    third_party_settings: {  }
    region: content
  field_city:
    type: string
    label: inline
    weight: -9
    settings: {  }
    third_party_settings: {  }
    region: content
  field_category:
    type: entity_reference_label
    label: inline
    weight: -8
    settings:
      link: true
    third_party_settings: {  }
    region: content
  field_price:
    type: number_decimal
    label: inline
    weight: -7
    settings:
      thousand_separator: ''
      decimal_separator: ','
      scale: 2
      prefix_suffix: true
    third_party_settings: {  }
    region: content
  field_social:
    type: link
    label: above
    weight: 0
    settings: {  }
    third_party_settings: {  }
    region: content
  field_available:
    type: boolean
    label: inline
    weight: -6
    settings:
      format: default
    third_party_settings: {  }
    region: content
  field_rating:
    type: number_decimal
    label: inline
    weight: -5
    settings:
      thousand_separator: ''
      decimal_separator: ','
      scale: 2
      prefix_suffix: false
    third_party_settings: {  }
    region: content
hidden:
  links: true
  created: true
  uid: true
