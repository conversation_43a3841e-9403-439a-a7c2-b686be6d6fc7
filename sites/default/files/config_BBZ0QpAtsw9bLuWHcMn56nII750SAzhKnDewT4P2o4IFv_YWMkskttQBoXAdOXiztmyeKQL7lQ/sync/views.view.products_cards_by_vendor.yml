langcode: fr
status: true
dependencies:
  module:
    - views
    - commerce_product
    - user
    - image
id: products_cards_by_vendor
label: 'Produits (cards) par vendor'
module: views
description: 'Bloc cartes (image, titre, prix). Utilise la classe .view-products-cards (module marketplace_cards_style).'
tag: ''
base_table: commerce_product_field_data
base_field: product_id
display:
  default:
    display_plugin: default
    id: default
    display_title: Master
    position: 0
    display_options:
      access:
        type: perm
        options:
          perm: 'access content'
      pager:
        type: some
        options:
          items_per_page: 12
      style:
        type: grid
        options:
          columns: 3
          automatic_width: true
          alignment: horizontal
      row:
        type: fields
        options: {  }
      relationships:
        variations:
          id: variations
          table: commerce_product__variations
          field: variations
          required: false
          plugin_id: standard
      fields:
        field_images:
          id: field_images
          table: commerce_product__field_images
          field: field_images
          label: ''
          plugin_id: field
          settings:
            image_style: medium
            image_link: content
        title:
          id: title
          table: commerce_product_field_data
          field: title
          label: ''
          plugin_id: field
          settings:
            link_to_entity: true
            alter:
              make_link: true
              path: ''
              alt: ''
          element_wrapper_type: div
          element_wrapper_class: product-title
        price__number:
          id: price__number
          table: commerce_product_variation_field_data
          field: price__number
          relationship: variations
          label: 'Prix'
          plugin_id: numeric
          settings:
            thousand_separator: ''
            decimal_separator: ','
            scale: 2
            prefix_suffix: true
          element_wrapper_type: div
          element_wrapper_class: product-price
      filters:
        status:
          id: status
          table: commerce_product_field_data
          field: status
          value: '1'
          plugin_id: boolean
      arguments:
        field_vendor_user_target_id:
          id: field_vendor_user_target_id
          table: commerce_product__field_vendor_user
          field: field_vendor_user_target_id
          plugin_id: numeric
          default_action: 'empty'
          title_enable: false
  block_by_vendor_uid:
    display_plugin: block
    id: block_by_vendor_uid
    display_title: 'Bloc: par vendor UID'
    position: 1
    display_options: {  }
  block_current_user:
    display_plugin: block
    id: block_current_user
    display_title: 'Bloc: vendor = user courant'
    position: 2
    display_options:
      arguments:
        field_vendor_user_target_id:
          id: field_vendor_user_target_id
          table: commerce_product__field_vendor_user
          field: field_vendor_user_target_id
          plugin_id: numeric
          default_action: 'default'
          default_argument_type: current_user
          title_enable: false
