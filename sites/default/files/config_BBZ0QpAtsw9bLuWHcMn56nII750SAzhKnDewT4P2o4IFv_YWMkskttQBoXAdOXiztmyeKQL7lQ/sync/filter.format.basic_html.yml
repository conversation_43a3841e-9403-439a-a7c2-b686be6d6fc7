uuid: 47bd27b7-89fd-4548-aed3-e9c8dd3817b7
langcode: en
status: true
dependencies:
  module:
    - editor
_core:
  default_config_hash: mclCbTlJwWJORez4Y1eX2MqA0aGjSMAoJb3TaBABcK8
name: 'Basic HTML'
format: basic_html
weight: 0
filters:
  editor_file_reference:
    id: editor_file_reference
    provider: editor
    status: true
    weight: 11
    settings: {  }
  filter_align:
    id: filter_align
    provider: filter
    status: true
    weight: 7
    settings: {  }
  filter_caption:
    id: filter_caption
    provider: filter
    status: true
    weight: 8
    settings: {  }
  filter_html:
    id: filter_html
    provider: filter
    status: true
    weight: -10
    settings:
      allowed_html: '<br> <p> <h2 id> <h3 id> <h4 id> <h5 id> <h6 id> <cite> <dl> <dt> <dd> <a hreflang href> <blockquote cite> <ul type> <ol start type> <strong> <em> <code> <li> <img src alt data-entity-uuid data-entity-type height width data-caption data-align>'
      filter_html_help: false
      filter_html_nofollow: false
  filter_html_image_secure:
    id: filter_html_image_secure
    provider: filter
    status: true
    weight: 9
    settings: {  }
  filter_image_lazy_load:
    id: filter_image_lazy_load
    provider: filter
    status: true
    weight: 15
    settings: {  }
