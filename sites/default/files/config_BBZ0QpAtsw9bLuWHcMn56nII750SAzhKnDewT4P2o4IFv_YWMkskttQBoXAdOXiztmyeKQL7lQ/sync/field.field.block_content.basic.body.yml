uuid: 5d5b30dd-a6f1-41dc-bfcf-11d9a00e87cf
langcode: en
status: true
dependencies:
  config:
    - block_content.type.basic
    - field.storage.block_content.body
  module:
    - text
_core:
  default_config_hash: j00Gfs9AFqwK4x9RIsISLbPO7vA12psPR4s60wOIwQo
id: block_content.basic.body
field_name: body
entity_type: block_content
bundle: basic
label: Body
description: ''
required: false
translatable: true
default_value: {  }
default_value_callback: ''
settings:
  display_summary: false
  required_summary: false
  allowed_formats: {  }
field_type: text_with_summary
