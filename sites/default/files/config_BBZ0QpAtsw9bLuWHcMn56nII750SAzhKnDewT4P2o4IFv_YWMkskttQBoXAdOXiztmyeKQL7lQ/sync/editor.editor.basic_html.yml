uuid: a02c4652-fdfa-4747-9226-d6471f7b527c
langcode: en
status: true
dependencies:
  config:
    - filter.format.basic_html
  module:
    - ckeditor5
_core:
  default_config_hash: rrcLa1jOgI7xezeJia25MS2tmm_3e6VZOQaCIPF9b-I
format: basic_html
editor: ckeditor5
settings:
  toolbar:
    items:
      - bold
      - italic
      - '|'
      - link
      - '|'
      - bulletedList
      - numberedList
      - '|'
      - blockQuote
      - drupalInsertImage
      - '|'
      - heading
      - code
      - '|'
      - sourceEditing
  plugins:
    ckeditor5_heading:
      enabled_headings:
        - heading2
        - heading3
        - heading4
        - heading5
        - heading6
    ckeditor5_imageResize:
      allow_resize: true
    ckeditor5_list:
      properties:
        reversed: false
        startIndex: true
      multiBlock: true
    ckeditor5_sourceEditing:
      allowed_tags:
        - '<cite>'
        - '<dl>'
        - '<dt>'
        - '<dd>'
        - '<a hreflang>'
        - '<blockquote cite>'
        - '<ul type>'
        - '<ol type>'
        - '<h2 id>'
        - '<h3 id>'
        - '<h4 id>'
        - '<h5 id>'
        - '<h6 id>'
image_upload:
  status: true
  scheme: public
  directory: inline-images
  max_size: ''
  max_dimensions:
    width: 0
    height: 0
