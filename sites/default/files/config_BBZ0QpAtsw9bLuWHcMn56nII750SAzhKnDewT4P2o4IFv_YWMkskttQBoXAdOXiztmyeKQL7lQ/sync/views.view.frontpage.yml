uuid: 9c7114d8-143f-4e27-ae51-be28bf8d834f
langcode: en
status: true
dependencies:
  config:
    - core.entity_view_mode.node.rss
    - core.entity_view_mode.node.teaser
  module:
    - node
    - user
_core:
  default_config_hash: 6eeliKIydPjqyv5V__QqTfahvJMWkHjOVUUuUIdB1ik
id: frontpage
label: Frontpage
module: node
description: 'All content promoted to the front page.'
tag: default
base_table: node_field_data
base_field: nid
display:
  default:
    id: default
    display_title: Default
    display_plugin: default
    position: 0
    display_options:
      title: ''
      fields: {  }
      pager:
        type: full
        options:
          offset: 0
          items_per_page: 10
          total_pages: 0
          id: 0
          tags:
            next: 'Next ›'
            previous: '‹ Previous'
            first: '« First'
            last: 'Last »'
          expose:
            items_per_page: false
            items_per_page_label: 'Items per page'
            items_per_page_options: '5, 10, 25, 50'
            items_per_page_options_all: false
            items_per_page_options_all_label: '- All -'
            offset: false
            offset_label: Offset
          quantity: 9
      exposed_form:
        type: basic
        options:
          submit_button: Apply
          reset_button: false
          reset_button_label: Reset
          exposed_sorts_label: 'Sort by'
          expose_sort_order: true
          sort_asc_label: Asc
          sort_desc_label: Desc
      access:
        type: perm
        options:
          perm: 'access content'
      cache:
        type: tag
        options: {  }
      empty:
        area_text_custom:
          id: area_text_custom
          table: views
          field: area_text_custom
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: text_custom
          label: ''
          empty: true
          content: 'No front page content has been created yet.<br/>Follow the <a target="_blank" href="https://www.drupal.org/docs/user_guide/en/index.html">User Guide</a> to start building your site.'
          tokenize: false
        node_listing_empty:
          id: node_listing_empty
          table: node
          field: node_listing_empty
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: node
          plugin_id: node_listing_empty
          label: ''
          empty: true
        title:
          id: title
          table: views
          field: title
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: title
          label: ''
          empty: true
          title: Welcome!
      sorts:
        sticky:
          id: sticky
          table: node_field_data
          field: sticky
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: node
          entity_field: sticky
          plugin_id: boolean
          order: DESC
          expose:
            label: ''
            field_identifier: sticky
          exposed: false
        created:
          id: created
          table: node_field_data
          field: created
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: node
          entity_field: created
          plugin_id: date
          order: DESC
          expose:
            label: ''
            field_identifier: created
          exposed: false
          granularity: second
      arguments: {  }
      filters:
        promote:
          id: promote
          table: node_field_data
          field: promote
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: node
          entity_field: promote
          plugin_id: boolean
          operator: '='
          value: '1'
          group: 1
          exposed: false
          expose:
            operator_id: ''
            label: ''
            description: ''
            use_operator: false
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
            identifier: ''
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
        status:
          id: status
          table: node_field_data
          field: status
          entity_type: node
          entity_field: status
          plugin_id: boolean
          value: '1'
          group: 1
          expose:
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
        langcode:
          id: langcode
          table: node_field_data
          field: langcode
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: node
          entity_field: langcode
          plugin_id: language
          operator: in
          value:
            '***LANGUAGE_language_content***': '***LANGUAGE_language_content***'
          group: 1
          exposed: false
          expose:
            operator_id: ''
            label: ''
            description: ''
            use_operator: false
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
            identifier: ''
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
            reduce: false
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
      style:
        type: default
        options:
          grouping: {  }
          row_class: ''
          default_row_class: true
          uses_fields: false
      row:
        type: 'entity:node'
        options:
          view_mode: teaser
      query:
        type: views_query
        options:
          query_comment: ''
          disable_sql_rewrite: false
          distinct: false
          replica: false
          query_tags: {  }
      relationships: {  }
      header: {  }
      footer: {  }
      display_extenders: {  }
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_interface'
        - url.query_args
        - 'user.node_grants:view'
        - user.permissions
      tags: {  }
  feed_1:
    id: feed_1
    display_title: Feed
    display_plugin: feed
    position: 2
    display_options:
      pager:
        type: some
        options:
          offset: 0
          items_per_page: 10
      style:
        type: rss
        options:
          grouping: {  }
          uses_fields: false
          description: ''
      row:
        type: node_rss
        options:
          relationship: none
          view_mode: rss
      display_extenders: {  }
      path: rss.xml
      sitename_title: true
      displays:
        page_1: page_1
        default: ''
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_interface'
        - 'user.node_grants:view'
        - user.permissions
      tags: {  }
  page_1:
    id: page_1
    display_title: Page
    display_plugin: page
    position: 1
    display_options:
      display_extenders: {  }
      path: node
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_interface'
        - url.query_args
        - 'user.node_grants:view'
        - user.permissions
      tags: {  }
