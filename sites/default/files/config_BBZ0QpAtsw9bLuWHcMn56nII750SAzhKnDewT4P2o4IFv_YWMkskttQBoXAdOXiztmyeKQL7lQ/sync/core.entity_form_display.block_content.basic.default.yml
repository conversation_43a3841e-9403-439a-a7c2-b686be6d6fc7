uuid: b7cde5ee-203e-4dc3-a0a0-1915c4b1b3ed
langcode: en
status: true
dependencies:
  config:
    - block_content.type.basic
    - field.field.block_content.basic.body
  module:
    - text
_core:
  default_config_hash: jAps3FCxvKecABS_tgExbhCZrBLQB3bNPWw18WjE3ss
id: block_content.basic.default
targetEntityType: block_content
bundle: basic
mode: default
content:
  body:
    type: text_textarea_with_summary
    weight: -4
    region: content
    settings:
      rows: 9
      summary_rows: 3
      placeholder: ''
      show_summary: false
    third_party_settings: {  }
  info:
    type: string_textfield
    weight: -5
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
hidden: {  }
