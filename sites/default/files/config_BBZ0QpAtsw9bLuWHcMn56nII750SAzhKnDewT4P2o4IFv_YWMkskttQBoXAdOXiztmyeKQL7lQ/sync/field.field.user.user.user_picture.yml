uuid: af124ae9-0b43-4301-8b95-a23fb6c6a9ff
langcode: en
status: true
dependencies:
  config:
    - field.storage.user.user_picture
  module:
    - image
    - user
_core:
  default_config_hash: TE3gYVzd6g0deXqUl8SEu2azHwVG-SdXm3kwbrz0kHw
id: user.user.user_picture
field_name: user_picture
entity_type: user
bundle: user
label: Picture
description: 'Your virtual face or picture.'
required: false
translatable: true
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:file'
  handler_settings: {  }
  file_directory: 'pictures/[date:custom:Y]-[date:custom:m]'
  file_extensions: 'png gif jpg jpeg webp'
  max_filesize: ''
  max_resolution: ''
  min_resolution: ''
  alt_field: false
  alt_field_required: false
  title_field: false
  title_field_required: false
  default_image:
    uuid: null
    alt: ''
    title: ''
    width: null
    height: null
field_type: image
