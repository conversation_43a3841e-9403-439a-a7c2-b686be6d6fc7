langcode: fr
status: true
dependencies:
  module:
    - views
    - node
    - user
id: mes_fiches
label: 'Mes fiches prestataire'
module: views
description: 'Tableau de bord des fiches du prestataire courant.'
tag: ''
base_table: node_field_data
base_field: nid
display:
  default:
    display_plugin: default
    id: default
    display_title: Master
    position: 0
    display_options:
      access:
        type: role
        options:
          role:
            prestataire: prestataire
      cache:
        type: tag
        options: {  }
      query:
        type: views_query
        options: {  }
      exposed_form:
        type: basic
        options: {  }
      pager:
        type: full
        options:
          items_per_page: 25
      style:
        type: table
        options:
          columns:
            title: title
            status: status
            changed: changed
          default: title
      row:
        type: fields
        options: {  }
      fields:
        title:
          id: title
          table: node_field_data
          field: title
          label: 'Titre'
          plugin_id: field
          settings:
            link_to_entity: true
        status:
          id: status
          table: node_field_data
          field: status
          label: 'Publié'
          plugin_id: boolean
          type: yes-no
        changed:
          id: changed
          table: node_field_data
          field: changed
          label: 'Mis à jour'
          plugin_id: date
          settings:
            date_format: medium
        edit_node:
          id: edit_node
          table: node_field_data
          field: edit_node
          label: 'Actions'
          plugin_id: entity_link_edit
      filters:
        type:
          id: type
          table: node_field_data
          field: type
          value:
            - prestataire
          plugin_id: bundle
      arguments:
        uid:
          id: uid
          table: node_field_data
          field: uid
          default_action: 'empty'
          exception:
            value: all
          plugin_id: user_uid
          default_argument_type: current_user
          title_enable: false
          specify_validation: false
  page_1:
    display_plugin: page
    id: page_1
    display_title: Page
    position: 1
    display_options:
      path: 'mon-espace/mes-fiches'
