uuid: 8f743ee6-e9c8-4815-ab0a-2929c9647daf
langcode: en
status: true
dependencies:
  config:
    - core.entity_view_mode.node.teaser
    - field.field.node.article.body
    - field.field.node.article.comment
    - field.field.node.article.field_image
    - field.field.node.article.field_tags
    - image.style.medium
    - node.type.article
  module:
    - image
    - text
    - user
_core:
  default_config_hash: 83jX5ChAGdMzOxiDA_B1NjgebVMuD8lNVDgClg_QVP8
id: node.article.teaser
targetEntityType: node
bundle: article
mode: teaser
content:
  body:
    type: text_summary_or_trimmed
    label: hidden
    settings:
      trim_length: 600
    third_party_settings: {  }
    weight: 0
    region: content
  field_image:
    type: image
    label: hidden
    settings:
      image_style: medium
      image_link: content
      image_loading:
        attribute: lazy
    third_party_settings: {  }
    weight: -1
    region: content
  field_tags:
    type: entity_reference_label
    label: above
    settings:
      link: true
    third_party_settings: {  }
    weight: 10
    region: content
  links:
    weight: 100
    region: content
hidden:
  comment: true
  field_image: true
  field_tags: true
