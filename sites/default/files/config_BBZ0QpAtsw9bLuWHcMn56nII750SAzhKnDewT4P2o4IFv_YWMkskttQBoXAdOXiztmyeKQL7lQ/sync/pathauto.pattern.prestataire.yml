langcode: fr
status: true
dependencies:
  module:
    - pathauto
id: prestataire
label: 'URL Prestataire'
type: 'canonical_entities:node'
pattern: 'prestataire/[node:title]'
selection_criteria:
  bundle:
    id: 'entity_bundle:node'
    negate: false
    uuid: 11111111-1111-4111-8111-111111111111
    context_mapping:
      node: node
    bundles:
      prestataire: prestataire
selection_logic: and
weight: 0
relationships: {  }
