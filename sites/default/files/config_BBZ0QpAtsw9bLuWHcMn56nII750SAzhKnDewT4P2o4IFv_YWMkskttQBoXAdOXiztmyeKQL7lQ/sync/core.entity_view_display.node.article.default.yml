uuid: 84971f2d-5886-47a0-8a5e-6d57c4d57de3
langcode: en
status: true
dependencies:
  config:
    - core.entity_view_display.comment.comment.default
    - field.field.node.article.body
    - field.field.node.article.comment
    - field.field.node.article.field_image
    - field.field.node.article.field_tags
    - image.style.wide
    - node.type.article
  module:
    - comment
    - image
    - text
    - user
_core:
  default_config_hash: br6izr-iGEu--JvNbCJNtOBpRnxpFLXfoV5y61U9Nqc
id: node.article.default
targetEntityType: node
bundle: article
mode: default
content:
  body:
    type: text_default
    label: hidden
    settings: {  }
    third_party_settings: {  }
    weight: 0
    region: content
  comment:
    type: comment_default
    label: above
    settings:
      view_mode: default
      pager_id: 0
    third_party_settings: {  }
    weight: 110
    region: content
  field_image:
    type: image
    label: hidden
    settings:
      image_link: ''
      image_style: wide
      image_loading:
        attribute: eager
    third_party_settings: {  }
    weight: -1
    region: content
  field_tags:
    type: entity_reference_label
    label: above
    settings:
      link: true
    third_party_settings: {  }
    weight: 10
    region: content
  links:
    settings: {  }
    third_party_settings: {  }
    weight: 100
    region: content
hidden: {  }
