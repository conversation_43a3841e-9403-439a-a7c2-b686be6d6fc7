langcode: fr
status: true
dependencies:
  config:
    - node.type.prestataire
  module:
    - node
id: node.prestataire.field_category
field_name: field_category
entity_type: node
bundle: prestataire
label: 'Catégorie'
description: ''
required: true
translatable: true
default_value: {}
default_value_callback: ''

      settings:
        handler: 'default:taxonomy_term'
        handler_settings:
          target_bundles:
            categories: categories

field_type: entity_reference
