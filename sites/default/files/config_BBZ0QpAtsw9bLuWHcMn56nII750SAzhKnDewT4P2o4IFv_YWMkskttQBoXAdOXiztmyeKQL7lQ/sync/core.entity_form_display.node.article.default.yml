uuid: 886c761a-16a3-4533-ad6c-4c5898229230
langcode: en
status: true
dependencies:
  config:
    - field.field.node.article.body
    - field.field.node.article.comment
    - field.field.node.article.field_image
    - field.field.node.article.field_tags
    - image.style.thumbnail
    - node.type.article
  module:
    - comment
    - image
    - path
    - text
_core:
  default_config_hash: ewbd6G2uX456-bgwseM2Q-KQG3RkASoyHmTh-XR3oLU
id: node.article.default
targetEntityType: node
bundle: article
mode: default
content:
  body:
    type: text_textarea_with_summary
    weight: 2
    region: content
    settings:
      rows: 9
      summary_rows: 3
      placeholder: ''
      show_summary: false
    third_party_settings: {  }
  comment:
    type: comment_default
    weight: 20
    region: content
    settings: {  }
    third_party_settings: {  }
  created:
    type: datetime_timestamp
    weight: 10
    region: content
    settings: {  }
    third_party_settings: {  }
  field_image:
    type: image_image
    weight: 1
    region: content
    settings:
      progress_indicator: throbber
      preview_image_style: thumbnail
    third_party_settings: {  }
  field_tags:
    type: entity_reference_autocomplete_tags
    weight: 3
    region: content
    settings:
      match_operator: CONTAINS
      match_limit: 10
      size: 60
      placeholder: ''
    third_party_settings: {  }
  path:
    type: path
    weight: 30
    region: content
    settings: {  }
    third_party_settings: {  }
  promote:
    type: boolean_checkbox
    weight: 15
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  status:
    type: boolean_checkbox
    weight: 120
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  sticky:
    type: boolean_checkbox
    weight: 16
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  title:
    type: string_textfield
    weight: 0
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  uid:
    type: entity_reference_autocomplete
    weight: 5
    region: content
    settings:
      match_operator: CONTAINS
      match_limit: 10
      size: 60
      placeholder: ''
    third_party_settings: {  }
hidden: {  }
