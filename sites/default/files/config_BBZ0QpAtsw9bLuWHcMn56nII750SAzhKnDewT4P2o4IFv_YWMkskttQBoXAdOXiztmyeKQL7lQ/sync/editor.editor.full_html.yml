uuid: baa3f60a-129e-400a-998c-b5a67e4d42dc
langcode: en
status: true
dependencies:
  config:
    - filter.format.full_html
  module:
    - ckeditor5
_core:
  default_config_hash: OPWefDnwy4tmy2n7dGK-VjZwSnqspgJ6m8148XlsA0A
format: full_html
editor: ckeditor5
settings:
  toolbar:
    items:
      - bold
      - italic
      - strikethrough
      - superscript
      - subscript
      - removeFormat
      - '|'
      - link
      - '|'
      - bulletedList
      - numberedList
      - '|'
      - blockQuote
      - drupalInsertImage
      - insertTable
      - horizontalLine
      - '|'
      - heading
      - codeBlock
      - '|'
      - sourceEditing
  plugins:
    ckeditor5_codeBlock:
      languages:
        -
          label: 'Plain text'
          language: plaintext
        -
          label: C
          language: c
        -
          label: 'C#'
          language: cs
        -
          label: C++
          language: cpp
        -
          label: CSS
          language: css
        -
          label: Diff
          language: diff
        -
          label: HTML
          language: html
        -
          label: Java
          language: java
        -
          label: JavaScript
          language: javascript
        -
          label: PHP
          language: php
        -
          label: Python
          language: python
        -
          label: Ruby
          language: ruby
        -
          label: TypeScript
          language: typescript
        -
          label: XML
          language: xml
    ckeditor5_heading:
      enabled_headings:
        - heading2
        - heading3
        - heading4
        - heading5
        - heading6
    ckeditor5_imageResize:
      allow_resize: true
    ckeditor5_list:
      properties:
        reversed: true
        startIndex: true
      multiBlock: true
    ckeditor5_sourceEditing:
      allowed_tags: {  }
image_upload:
  status: true
  scheme: public
  directory: inline-images
  max_size: ''
  max_dimensions:
    width: 0
    height: 0
