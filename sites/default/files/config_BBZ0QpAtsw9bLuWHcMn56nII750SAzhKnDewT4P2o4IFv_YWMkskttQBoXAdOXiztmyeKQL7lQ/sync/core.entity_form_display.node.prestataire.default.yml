langcode: fr
status: true
dependencies:
  config:
    - node.type.prestataire
id: node.prestataire.default
targetEntityType: node
bundle: prestataire
mode: default
content:
  title:
    type: string_textfield
    weight: -10
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
    region: content
  field_logo:
    type: image_image
    weight: -9
    settings: {  }
    third_party_settings: {  }
    region: content
  field_city:
    type: string_textfield
    weight: -8
    settings: {  }
    third_party_settings: {  }
    region: content
  field_category:
    type: options_select
    weight: -7
    settings: {  }
    third_party_settings: {  }
    region: content
  field_price:
    type: number
    weight: -6
    settings: {  }
    third_party_settings: {  }
    region: content
  field_available:
    type: boolean_checkbox
    weight: -5
    settings:
      display_label: true
    third_party_settings: {  }
    region: content
  field_social:
    type: link_default
    weight: -4
    settings: {  }
    third_party_settings: {  }
    region: content
  field_rating:
    type: number
    weight: -3
    settings: {  }
    third_party_settings: {  }
    region: content
hidden: {  }
