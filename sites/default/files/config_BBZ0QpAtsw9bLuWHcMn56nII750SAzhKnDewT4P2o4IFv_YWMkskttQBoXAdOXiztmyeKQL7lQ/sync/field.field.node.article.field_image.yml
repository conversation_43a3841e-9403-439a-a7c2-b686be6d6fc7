uuid: 8dab7b19-**************-07dd45310d9b
langcode: en
status: true
dependencies:
  config:
    - field.storage.node.field_image
    - node.type.article
  module:
    - image
_core:
  default_config_hash: j0riO_-77ZFWNLtj0iJ31HnohiNjdn8HUL86RueCF-M
id: node.article.field_image
field_name: field_image
entity_type: node
bundle: article
label: Image
description: ''
required: false
translatable: true
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:file'
  handler_settings: {  }
  file_directory: '[date:custom:Y]-[date:custom:m]'
  file_extensions: 'png gif jpg jpeg webp'
  max_filesize: ''
  max_resolution: ''
  min_resolution: ''
  alt_field: true
  alt_field_required: true
  title_field: false
  title_field_required: false
  default_image:
    uuid: null
    alt: ''
    title: ''
    width: null
    height: null
field_type: image
