langcode: fr
status: true
dependencies:
  module:
    - views
    - node
    - taxonomy
    - user
id: prestataires_directory
label: 'Annuaire des prestataires'
module: views
description: 'Listing public des prestataires, avec filtres exposés.'
tag: ''
base_table: node_field_data
base_field: nid
display:
  default:
    display_plugin: default
    id: default
    display_title: Master
    position: 0
    display_options:
      access:
        type: perm
        options:
          perm: 'access content'
      cache:
        type: tag
        options: {  }
      query:
        type: views_query
        options:
          disable_sql_rewrite: false
          distinct: false
          replica: false
          query_comment: ''
          query_tags: [ ]
      exposed_form:
        type: basic
        options:
          submit_button: Appliquer
          reset_button: true
          reset_button_label: Réinitialiser
          exposed_sorts_label: 'Trier par'
          expose_sort_order: true
      pager:
        type: full
        options:
          items_per_page: 12
          offset: 0
      style:
        type: grid
        options:
          columns: 3
          automatic_width: true
          alignment: horizontal
      row:
        type: fields
        options: {  }
      fields:
        title:
          id: title
          table: node_field_data
          field: title
          label: ''
          settings:
            link_to_entity: true
          plugin_id: field
          exclude: false
          element_default_classes: true
          type: string
        field_logo:
          id: field_logo
          table: node__field_logo
          field: field_logo
          label: ''
          plugin_id: field
          settings:
            image_style: thumbnail
            image_link: content
        field_city:
          id: field_city
          table: node__field_city
          field: field_city
          label: 'Ville'
          plugin_id: field
        field_category:
          id: field_category
          table: node__field_category
          field: field_category
          label: 'Catégorie'
          plugin_id: field
          settings:
            link: true
        field_price:
          id: field_price
          table: node__field_price
          field: field_price
          label: 'Tarif indicatif'
          plugin_id: field
          type: number_decimal
          settings:
            thousand_separator: ''
            decimal_separator: ','
            scale: 2
            prefix_suffix: true
      filters:
        status:
          id: status
          table: node_field_data
          field: status
          value: '1'
          plugin_id: boolean
          group: 1
          exposed: false
        type:
          id: type
          table: node_field_data
          field: type
          value:
            - prestataire
          plugin_id: bundle
        field_category_target_id:
          id: field_category_target_id
          table: node__field_category
          field: field_category_target_id
          plugin_id: taxonomy_index_tid
          group: 1
          exposed: true
          expose:
            operator_id: field_category_target_id_op
            label: 'Catégorie'
            use_operator: false
            identifier: field_category
            required: false
          is_grouped: false
        field_city_value:
          id: field_city_value
          table: node__field_city
          field: field_city_value
          plugin_id: string
          operator: contains
          exposed: true
          expose:
            label: 'Ville'
            identifier: field_city
            required: false
        field_price_value:
          id: field_price_value
          table: node__field_price
          field: field_price_value
          plugin_id: numeric
          operator: between
          exposed: true
          expose:
            label: 'Tarif (min–max)'
            identifier: field_price_between
            required: false
      sorts:
        field_rating_value:
          id: field_rating_value
          table: node__field_rating
          field: field_rating_value
          order: DESC
          plugin_id: standard
        title:
          id: title
          table: node_field_data
          field: title
          order: ASC
          plugin_id: standard
  page_1:
    display_plugin: page
    id: page_1
    display_title: Page
    position: 1
    display_options:
      path: 'prestataires'
      menu:
        type: normal
        title: 'Prestataires'
        description: ''
        menu_name: main
        weight: 0
        context: '0'
