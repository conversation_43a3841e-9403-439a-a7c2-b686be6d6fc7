uuid: 061ec82b-9a8e-4fb2-bfcf-1e19549a5814
langcode: en
status: true
dependencies:
  config:
    - field.storage.node.field_tags
    - node.type.article
    - taxonomy.vocabulary.tags
_core:
  default_config_hash: QdUgf_beeoaPiyKorFv0q1fcJpWH_uZTqe_xoVJacrw
id: node.article.field_tags
field_name: field_tags
entity_type: node
bundle: article
label: Tags
description: 'Enter a comma-separated list. For example: Amsterdam, Mexico City, "Cleveland, Ohio"'
required: false
translatable: true
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:taxonomy_term'
  handler_settings:
    target_bundles:
      tags: tags
    sort:
      field: _none
    auto_create: true
field_type: entity_reference
