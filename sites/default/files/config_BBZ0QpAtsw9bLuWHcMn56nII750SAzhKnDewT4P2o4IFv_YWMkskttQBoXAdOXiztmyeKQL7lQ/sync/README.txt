Drupal 10.2 – Export de configuration (Prestataire + Vues)

1) Prérequis modules:
   - node (Core)
   - taxonomy (Core)
   - views (Core)
   - image (Core)
   - link (Core)
   - layout_builder (Core)
   - pathauto (Contrib) – optionnel mais recommandé

2) Copiez tous les fichiers .yml dans votre dossier de synchro config, par ex.:
   web/sites/default/files/config_HASH/sync/
   (ou le dossier défini dans settings.php: $config_directories['sync'])

3) Importez la configuration:
   drush cim -y
   drush cr

4) Vérifications rapides:
   - Type de contenu "Prestataire" présent avec ses champs
   - Vocabulaire "Catégories"
   - Vues:
       /prestataires
       /mon-espace/mes-fiches
   - Rôle "prestataire" créé
   - Layout Builder activé pour "Prestataire"

5) Notes:
   - Le filtre "Tarif (min–max)" utilise l'opérateur "entre".
   - Ajoutez vos termes dans Taxonomie > Catégories.
   - Adaptez les permissions selon vos workflows.
