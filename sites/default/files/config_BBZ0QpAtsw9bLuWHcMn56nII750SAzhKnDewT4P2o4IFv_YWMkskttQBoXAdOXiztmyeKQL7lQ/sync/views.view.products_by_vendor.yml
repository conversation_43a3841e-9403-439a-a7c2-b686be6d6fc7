uuid: 5d78b4c8-cd77-4b4f-830c-42f70da9ff06
langcode: fr
status: true
dependencies:
  module:
    - commerce_product
    - user
_core:
  default_config_hash: e4y2NsY105PUr4tx7ObpDGuL3s5ak-oBwiggJnlPgvw
id: products_by_vendor
label: 'Produits par vendor (argument UID)'
module: views
description: 'Bloc listant les produits pour un UID donné (field_vendor_user).'
tag: ''
base_table: commerce_product_field_data
base_field: product_id
display:
  default:
    display_plugin: default
    id: default
    display_title: Master
    position: 0
    display_options:
      access:
        type: perm
        options:
          perm: 'access content'
      cache:
        type: tag
        options: {  }
      query:
        type: views_query
        options: {  }
      exposed_form:
        type: basic
        options: {  }
      pager:
        type: some
        options:
          items_per_page: 12
      style:
        type: html_list
        options: {  }
      row:
        type: fields
        options: {  }
      fields:
        title:
          id: title
          table: commerce_product_field_data
          field: title
          label: ''
          plugin_id: field
          settings:
            link_to_entity: true
      filters:
        status:
          id: status
          table: commerce_product_field_data
          field: status
          value: '1'
          plugin_id: boolean
      arguments:
        field_vendor_user_target_id:
          id: field_vendor_user_target_id
          table: commerce_product__field_vendor_user
          field: field_vendor_user_target_id
          plugin_id: numeric
          default_action: empty
          title_enable: false
          specify_validation: false
      display_extenders: {  }
  block_1:
    display_plugin: block
    id: block_1
    display_title: Bloc
    position: 1
    display_options:
      display_extenders: {  }
